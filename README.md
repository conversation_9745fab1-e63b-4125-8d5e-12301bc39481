# نظام التفعيل الكامل - CIH99 Activation System

نظام تفعيل متقدم وشامل للإضافات يمكن استخدامه في أي مشروع Chrome Extension. يوفر النظام حماية قوية وإدارة متقدمة لحالة التفعيل.

## المميزات الرئيسية

### 🔐 نظام الحماية
- **التحقق من كلمة المرور عبر الإنترنت**: يتحقق من كلمات المرور من خادم خارجي
- **مراقبة تغيير كلمات المرور**: يكتشف تلقائياً أي تغيير في كلمات المرور ويعطل الإضافة
- **حماية من التخزين المؤقت**: يتجنب مشاكل التخزين المؤقت للحصول على أحدث كلمات المرور

### 🎛️ إدارة التفعيل
- **تفعيل/تعطيل ديناميكي**: إمكانية تفعيل وتعطيل الإضافة في الوقت الفعلي
- **حفظ الحالة**: يحفظ حالة التفعيل ويستعيدها عند إعادة تشغيل المتصفح
- **تحديث تلقائي**: يحدث جميع التابات المفتوحة عند تغيير الحالة

### 🎨 واجهة المستخدم
- **تصميم احترافي**: واجهة مستخدم جذابة ومتجاوبة
- **رسائل تفاعلية**: رسائل واضحة للمستخدم مع إمكانية الإغلاق
- **أنيميشن متقدم**: تأثيرات بصرية جذابة

### 🔔 نظام الإشعارات
- **إشعارات المتصفح**: إشعارات تلقائية عند تغيير كلمة المرور
- **رسائل في الواجهة**: رسائل فورية في النافذة المنبثقة

## الملفات المتضمنة

### الملفات الأساسية
- `activation-system.js` - النظام الأساسي للتفعيل
- `activation-ui.js` - واجهة المستخدم
- `activation-background.js` - سكريبت الخلفية
- `activation-content.js` - سكريبت المحتوى
- `activation-popup.html` - النافذة المنبثقة
- `activation-manifest.json` - ملف البيان

### ملفات التوثيق
- `README.md` - هذا الملف
- `INSTALLATION.md` - دليل التثبيت
- `API.md` - توثيق API

## كيفية الاستخدام

### 1. التثبيت الأساسي

```javascript
// في background.js
const activationSystem = new ActivationSystem({
    passwordUrl: 'https://your-domain.com/passwords.json',
    checkInterval: 30000, // 30 ثانية
    extensionName: 'اسم إضافتك'
});
```

### 2. إعداد واجهة المستخدم

```javascript
// في popup.js
const activationUI = new ActivationUI({
    passwordUrl: 'https://your-domain.com/passwords.json',
    telegramLink: 'https://t.me/your_channel',
    passwordPageLink: 'https://your-domain.com/password-page',
    extensionName: 'اسم إضافتك'
});
```

### 3. تخصيص سكريبت المحتوى

```javascript
// في content.js - تخصيص الوظائف حسب نوع إضافتك
function applyExtensionFeatures() {
    // ضع هنا وظائف إضافتك الخاصة
    hideAdBlockDetectors();
    applyCustomStyles();
    // ... وظائف أخرى
}
```

## إعداد ملف كلمات المرور

يجب إنشاء ملف JSON على خادم خارجي بالتنسيق التالي:

```json
{
    "passwords": ["password1", "password2", "password3"],
    "additionalPasswords": ["extra1", "extra2"],
    "specialPasswords": ["special1", "special2"]
}
```

## التخصيص

### تغيير الألوان والتصميم

يمكنك تخصيص الألوان في ملف `activation-popup.html`:

```css
:root {
    --primary-color: #ff0000;
    --secondary-color: #cc0000;
    --background-color: #1a0000;
    --text-color: #ffffff;
}
```

### إضافة وظائف مخصصة

في `activation-content.js`، يمكنك إضافة وظائفك الخاصة:

```javascript
function applyExtensionFeatures() {
    if (!extensionEnabled) return;
    
    // وظائفك المخصصة هنا
    myCustomFunction();
    anotherCustomFunction();
}
```

## الأمان

### حماية كلمات المرور
- يتم تخزين كلمات المرور على خادم خارجي آمن
- لا يتم تخزين كلمات المرور في الإضافة نفسها
- يتم التحقق من التغييرات دورياً

### حماية من التلاعب
- مراقبة مستمرة لتغيير كلمات المرور
- تعطيل تلقائي عند اكتشاف تغييرات
- إشعارات فورية للمستخدم

## استكشاف الأخطاء

### مشاكل شائعة

1. **لا يتم التحقق من كلمة المرور**
   - تأكد من صحة رابط ملف كلمات المرور
   - تحقق من إعدادات CORS على الخادم

2. **لا تعمل الإضافة بعد التفعيل**
   - تحقق من console للأخطاء
   - تأكد من تحديث جميع التابات

3. **مشاكل في الواجهة**
   - تحقق من تحميل ملفات CSS و JS
   - تأكد من صحة معرفات العناصر

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- Telegram: [@cih99x](https://t.me/cih99x)
- GitHub Issues: [رابط المشروع]

## الترخيص

هذا النظام مطور بواسطة CIH99 ويمكن استخدامه في المشاريع الشخصية والتجارية.

## التحديثات

### الإصدار 1.0.0
- النسخة الأولى من النظام
- جميع الميزات الأساسية
- واجهة مستخدم كاملة
- نظام حماية متقدم

---

**ملاحظة**: تأكد من قراءة دليل التثبيت الكامل في ملف `INSTALLATION.md` قبل البدء في الاستخدام.
