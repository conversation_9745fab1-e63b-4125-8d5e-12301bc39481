// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
  // تفعيل/تعطيل الإضافة
  const toggleSwitch = document.getElementById('toggleSwitch');
  const statusText = document.getElementById('statusText');
  const descriptionText = document.getElementById('descriptionText');
  const iconContainer = document.querySelector('.icon-container');
  const passwordSection = document.getElementById('passwordSection');
  const passwordInput = document.getElementById('passwordInput');
  const submitPassword = document.getElementById('submitPassword');
  const passwordLink = document.getElementById('passwordLink');

  // متغيرات لنظام كلمة المرور
  let passwordRequired = false;

  // دالة لتحديث حالة الأيقونة والنص
  function updateIconVisibility(isEnabled) {
    if (isEnabled) {
      // إخفاء الأيقونة عندما تكون الإضافة نشطة
      iconContainer.style.display = 'none';
      // تغيير النص إلى "وضع إخفاء الكاشف" مع إضافة تأثير حركي
      descriptionText.textContent = 'وضع إخفاء الكاشف';
      descriptionText.classList.add('text-glow'); // إضافة فئة التأثير الحركي
    } else {
      // إظهار الأيقونة عندما تكون الإضافة معطلة
      iconContainer.style.display = 'flex';
      // إعادة النص إلى "تم اكتشاف مانع الإعلانات" وإزالة التأثير الحركي
      descriptionText.textContent = 'تم اكتشاف مانع الإعلانات';
      descriptionText.classList.remove('text-glow'); // إزالة فئة التأثير الحركي
    }
  }

  // دالة للتحقق من حالة كلمة المرور
  function checkPasswordStatus() {
    chrome.storage.local.get(['passwordRequired', 'passwordVerified'], function(data) {
      // تحديث المتغيرات المحلية
      passwordRequired = data.passwordRequired || false;
      const passwordVerified = data.passwordVerified || false;

      // إذا كانت كلمة المرور مطلوبة ولم يتم التحقق منها بعد
      if (passwordRequired && !passwordVerified) {
        // عرض قسم كلمة المرور
        passwordSection.style.display = 'block';
      } else {
        // إخفاء قسم كلمة المرور
        passwordSection.style.display = 'none';
      }
    });
  }

  // دالة لعرض رسالة في صندوق الرسائل
  function showMessage(message, isSuccess = false) {
    const messageBox = document.getElementById('messageBox');
    const messageText = document.getElementById('messageText');

    messageText.textContent = message;

    // إضافة أو إزالة فئة النجاح
    if (isSuccess) {
      messageBox.classList.add('success');
    } else {
      messageBox.classList.remove('success');
    }

    // إظهار صندوق الرسائل
    messageBox.style.display = 'block';

    // إخفاء الرسالة تلقائيًا بعد 5 ثوانٍ
    setTimeout(() => {
      messageBox.style.display = 'none';
    }, 5000);
  }

  // دالة للتحقق من صحة كلمة المرور
  function verifyPassword(password) {
    console.log('كلمة المرور المدخلة:', password);

    // إظهار رسالة انتظار
    showMessage('جاري التحقق من كلمة المرور...', false);

    // إضافة معلمة عشوائية لتجنب التخزين المؤقت
    const randomParam = Math.random();
    const url = `https://abdelhalimx5.github.io/cih99-passwords/passwords.json?nocache=${randomParam}`;

    // إرسال طلب للتحقق من كلمة المرور
    fetch(url, {
      method: 'GET',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
      .then(response => {
        console.log('استجابة الخادم:', response.status, response.statusText);
        if (!response.ok) {
          throw new Error(`فشل الطلب: ${response.status} ${response.statusText}`);
        }
        return response.json();
      })
      .then(data => {
        console.log('بيانات كلمات المرور المستلمة:', data);
        console.log('نوع البيانات:', typeof data);

        // تحديث آخر بيانات كلمات المرور تم التحقق منها في التخزين المحلي
        chrome.storage.local.set({ 'lastPasswordsJSON': JSON.stringify(data) }, function() {
          console.log('تم تحديث آخر بيانات كلمات المرور في التخزين المحلي');
        });

        // التحقق مما إذا كانت البيانات فارغة أو غير صالحة
        if (!data || typeof data !== 'object') {
          console.error('البيانات المستلمة غير صالحة:', data);
          showMessage('حدث خطأ في البيانات المستلمة. يرجى المحاولة مرة أخرى لاحقًا.');
          return;
        }

        // الحصول على جميع القيم في الملف
        const allPasswords = Object.values(data);
        console.log('جميع كلمات المرور المتاحة:', allPasswords);

        // التحقق مما إذا كان الملف فارغًا
        if (allPasswords.length === 0) {
          console.error('لا توجد كلمات مرور في الملف');
          showMessage('لا توجد كلمات مرور متاحة. يرجى التحقق من ملف كلمات المرور.');
          return;
        }

        // طباعة كل كلمة مرور للتشخيص
        console.log('مقارنة كلمة المرور المدخلة مع كل كلمة مرور متاحة:');
        allPasswords.forEach((pwd, index) => {
          console.log(`كلمة المرور ${index + 1}:`, pwd);
          console.log(`هل تتطابق مع المدخلة؟`, pwd === password);
        });

        // التحقق مما إذا كانت كلمة المرور موجودة في القائمة
        if (allPasswords.includes(password)) {
          console.log('كلمة المرور صحيحة!');
          // كلمة المرور صحيحة
          chrome.storage.local.set({
            passwordVerified: true,
            passwordRequired: false  // إضافة هذا السطر لإلغاء طلب كلمة المرور
          }, function() {
            console.log('تم تحديث حالة التحقق من كلمة المرور');

            // تفعيل الإضافة
            toggleSwitch.checked = true;
            statusText.textContent = 'نشط';
            chrome.runtime.sendMessage({action: 'enable'});
            updateIconVisibility(true);

            // عرض رسالة نجاح
            showMessage('تم تفعيل الإضافة بنجاح!', true);

            // إخفاء قسم كلمة المرور بعد ثانية واحدة
            setTimeout(() => {
              passwordSection.style.display = 'none';
            }, 1000);
          });
        } else {
          console.error('كلمة المرور غير صحيحة!');
          console.log('كلمة المرور المدخلة:', password);
          console.log('كلمات المرور المتاحة:', allPasswords);

          // كلمة المرور غير صحيحة - رسالة بسيطة للمستخدم
          showMessage('كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى أو زيارة صفحة الويب للحصول على كلمة المرور الجديدة.');
        }
      })
      .catch(error => {
        console.error('خطأ في التحقق من كلمة المرور:', error);
        showMessage('حدث خطأ أثناء التحقق من كلمة المرور. يرجى المحاولة مرة أخرى لاحقًا.');
      });
  }

  // تحديث حالة الأيقونة عند بدء التشغيل (الافتراضي: نشط)
  updateIconVisibility(true);

  // إضافة مستمع لزر إغلاق الرسالة
  document.getElementById('closeMessage').addEventListener('click', function() {
    document.getElementById('messageBox').style.display = 'none';
  });

  // التحقق من تغيير كلمة المرور في كل مرة يتم فيها فتح النافذة المنبثقة
  chrome.runtime.sendMessage({action: 'checkPasswordChange'}, function(_) {
    // التحقق من وجود خطأ
    if (chrome.runtime.lastError) {
      // تجاهل الخطأ - النافذة المنبثقة قد تكون أغلقت
      console.log('تم تجاهل خطأ: ', chrome.runtime.lastError.message);
      return;
    }

    // بعد التحقق من تغيير كلمة المرور، تحقق من حالة كلمة المرور
    checkPasswordStatus();
  });

  // آلية بديلة للتحقق من تغيير كلمة المرور مباشرة من النافذة المنبثقة
  function checkPasswordChangeDirectly() {
    console.log('التحقق المباشر من تغيير كلمة المرور...');

    // إضافة معلمة عشوائية لتجنب التخزين المؤقت
    const randomParam = Math.random();
    const url = `https://abdelhalimx5.github.io/cih99-passwords/passwords.json?nocache=${randomParam}`;

    fetch(url, {
      method: 'GET',
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`فشل الطلب: ${response.status} ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      // التحقق من صحة البيانات
      if (!data || typeof data !== 'object') {
        console.error('البيانات المستلمة غير صالحة:', data);
        return;
      }

      const currentPasswordsJSON = JSON.stringify(data);
      console.log('تم استلام بيانات كلمات المرور الحالية (مباشر):', data);

      chrome.storage.local.get(['lastPasswordsJSON', 'enabled', 'passwordVerified'], function(result) {
        // إذا كان lastPasswordsJSON غير موجود، قم بتعيينه فقط دون تغيير حالة الإضافة
        if (!result.lastPasswordsJSON) {
          console.log('لا توجد بيانات سابقة لكلمات المرور. تعيين البيانات الحالية كأول بيانات.');
          chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON }, function() {
            console.log('تم تعيين بيانات كلمات المرور الأولية في التخزين المحلي');
          });
          return;
        }

        const lastPasswordsJSON = result.lastPasswordsJSON;

        console.log('آخر بيانات كلمات المرور تم التحقق منها (مباشر):', lastPasswordsJSON);
        console.log('هل البيانات متطابقة (مباشر)؟', currentPasswordsJSON === lastPasswordsJSON);

        // التحقق من تغيير كلمات المرور بشكل أكثر دقة
        try {
          const lastPasswords = JSON.parse(lastPasswordsJSON);

          // التحقق من أن كلا البيانات صالحة
          if (!lastPasswords || typeof lastPasswords !== 'object') {
            console.error('البيانات السابقة غير صالحة. تحديث البيانات فقط دون تغيير حالة الإضافة.');
            chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
            return;
          }

          // الحصول على كلمات المرور من كلا البيانات
          const oldPasswords = Object.values(lastPasswords);
          const newPasswords = Object.values(data);

          console.log('كلمات المرور القديمة:', oldPasswords);
          console.log('كلمات المرور الجديدة:', newPasswords);

          // التحقق مما إذا كانت كلمات المرور قد تغيرت فعلاً
          let passwordsChanged = false;

          // التحقق من أن عدد كلمات المرور مختلف
          if (oldPasswords.length !== newPasswords.length) {
            passwordsChanged = true;
            console.log('عدد كلمات المرور تغير.');
          } else {
            // التحقق من كل كلمة مرور
            for (let i = 0; i < newPasswords.length; i++) {
              if (!oldPasswords.includes(newPasswords[i])) {
                passwordsChanged = true;
                console.log(`كلمة المرور الجديدة غير موجودة في القائمة القديمة: ${newPasswords[i]}`);
                break;
              }
            }
          }

          if (passwordsChanged) {
            console.log('!!! تم اكتشاف تغيير في كلمات المرور (مباشر) !!!');

            // تحديث آخر بيانات كلمات المرور تم التحقق منها في التخزين المحلي
            chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON }, function() {
              console.log('تم تحديث آخر بيانات كلمات المرور في التخزين المحلي (مباشر)');
            });

            // تعيين حالة كلمة المرور إلى "مطلوبة" وإعادة تعيين حالة التحقق
            chrome.storage.local.set({
              'passwordRequired': true,
              'passwordVerified': false,
              'enabled': false
            }, function() {
              console.log('تم تعطيل الإضافة وطلب كلمة المرور الجديدة (مباشر)');

              // تحديث واجهة المستخدم
              toggleSwitch.checked = false;
              statusText.textContent = 'معطل';
              updateIconVisibility(false);

              // إظهار قسم كلمة المرور
              passwordSection.style.display = 'block';

              // عرض رسالة للمستخدم
              showMessage('تم تغيير كلمة المرور. يرجى إدخال كلمة المرور الجديدة.', false);
            });
          } else {
            console.log('لم يتم اكتشاف أي تغيير في كلمات المرور (مباشر)');
            // تحديث البيانات فقط للتأكد من أن التنسيق متطابق
            if (currentPasswordsJSON !== lastPasswordsJSON) {
              chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
            }
          }
        } catch (e) {
          console.error('خطأ في تحليل البيانات السابقة:', e);
          // تحديث البيانات فقط دون تغيير حالة الإضافة
          chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
        }
      });
    })
    .catch(error => {
      console.error('خطأ في التحقق المباشر من تغيير كلمة المرور:', error);
    });
  }

  // تنفيذ التحقق المباشر من تغيير كلمة المرور
  checkPasswordChangeDirectly();

  // التحقق من حالة كلمة المرور عند بدء التشغيل
  checkPasswordStatus();

  // إضافة مستمع لزر تقديم كلمة المرور
  submitPassword.addEventListener('click', function() {
    const password = passwordInput.value.trim();
    if (password) {
      verifyPassword(password);
    } else {
      showMessage('يرجى إدخال كلمة المرور.');
    }
  });

  // إضافة مستمع لحدث الضغط على Enter في حقل كلمة المرور
  passwordInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      const password = passwordInput.value.trim();
      if (password) {
        verifyPassword(password);
      } else {
        showMessage('يرجى إدخال كلمة المرور.');
      }
    }
  });

  // إضافة مستمع لرابط كلمة المرور
  passwordLink.addEventListener('click', function(e) {
    e.preventDefault();
    // فتح صفحة كلمة المرور في نافذة جديدة
    chrome.tabs.create({ url: 'https://abdelhalimx5.github.io/cih99-passwords/index.html' });
  });

  toggleSwitch.addEventListener('change', function() {
    if (this.checked) {
      // التحقق من حالة كلمة المرور قبل التفعيل
      chrome.storage.local.get(['passwordRequired', 'passwordVerified'], function(data) {
        if (data.passwordRequired && !data.passwordVerified) {
          // إذا كانت كلمة المرور مطلوبة ولم يتم التحقق منها، لا تسمح بالتفعيل
          toggleSwitch.checked = false;
          statusText.textContent = 'معطل';
          showMessage('يرجى إدخال كلمة المرور لتفعيل الإضافة.');
        } else {
          // إذا لم تكن كلمة المرور مطلوبة أو تم التحقق منها، قم بالتفعيل
          statusText.textContent = 'نشط';
          // إرسال رسالة لتفعيل الإضافة
          chrome.runtime.sendMessage({action: 'enable'});
          // إخفاء الأيقونة
          updateIconVisibility(true);
        }
      });
    } else {
      statusText.textContent = 'معطل';
      // إرسال رسالة لتعطيل الإضافة
      chrome.runtime.sendMessage({action: 'disable'});
      // إظهار الأيقونة
      updateIconVisibility(false);
    }
  });

  // التحقق من حالة الإضافة عند فتح النافذة المنبثقة
  chrome.storage.local.get('enabled', function(data) {
    if (data.enabled === false) {
      toggleSwitch.checked = false;
      statusText.textContent = 'معطل';
      // إظهار الأيقونة لأن الإضافة معطلة
      updateIconVisibility(false);
    } else {
      // التحقق من حالة كلمة المرور
      chrome.storage.local.get(['passwordRequired', 'passwordVerified'], function(pwData) {
        if (pwData.passwordRequired && !pwData.passwordVerified) {
          // إذا كانت كلمة المرور مطلوبة ولم يتم التحقق منها، قم بتعطيل الإضافة
          toggleSwitch.checked = false;
          statusText.textContent = 'معطل';
          updateIconVisibility(false);
          chrome.runtime.sendMessage({action: 'disable'});
        } else {
          // إخفاء الأيقونة لأن الإضافة نشطة
          updateIconVisibility(true);
        }
      });
    }
  });

  // إضافة رابط التليجرام
  const telegramLink = document.getElementById('telegramLink');
  telegramLink.addEventListener('click', function(e) {
    e.preventDefault();
    // فتح رابط التليجرام في نافذة جديدة
    chrome.tabs.create({ url: 'https://t.me/GurusVIP' });
  });
});
