/**
 * Content Script لنظام التفعيل - CIH99 Activation System
 * يعمل في صفحات الويب ويطبق وظائف الإضافة حسب حالة التفعيل
 */

(function() {
    'use strict';
    
    // متغير لتخزين حالة الإضافة
    let extensionEnabled = true;
    let isInitialized = false;
    
    // إعدادات النظام
    const config = {
        // يمكن تخصيص هذه الإعدادات حسب نوع الإضافة
        enableLogs: true,
        autoStart: true,
        checkInterval: 1000 // مللي ثانية
    };
    
    /**
     * تسجيل الرسائل (اختياري)
     */
    function log(message, type = 'info') {
        if (!config.enableLogs) return;
        
        const prefix = '[CIH99 Activation]';
        switch (type) {
            case 'error':
                console.error(prefix, message);
                break;
            case 'warn':
                console.warn(prefix, message);
                break;
            default:
                console.log(prefix, message);
        }
    }
    
    /**
     * تهيئة النظام
     */
    function initialize() {
        if (isInitialized) return;
        
        log('تهيئة نظام التفعيل في المحتوى...');
        
        // التحقق من حالة الإضافة المحفوظة
        checkExtensionStatus();
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        // بدء المراقبة إذا كانت الإضافة مفعلة
        if (extensionEnabled && config.autoStart) {
            startExtensionFeatures();
        }
        
        isInitialized = true;
        log('تم تهيئة نظام التفعيل بنجاح');
    }
    
    /**
     * التحقق من حالة الإضافة
     */
    function checkExtensionStatus() {
        // طلب حالة الإضافة من background script
        chrome.runtime.sendMessage({action: 'getStatus'}, (response) => {
            if (chrome.runtime.lastError) {
                log('خطأ في الحصول على حالة الإضافة: ' + chrome.runtime.lastError.message, 'error');
                return;
            }
            
            if (response && typeof response.enabled === 'boolean') {
                updateExtensionStatus(response.enabled);
            }
        });
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // الاستماع للرسائل من background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateStatus') {
                updateExtensionStatus(message.enabled);
                sendResponse({success: true});
            }
            return false;
        });
        
        // مراقبة تغييرات DOM
        if (document.body) {
            setupDOMObserver();
        } else {
            // انتظار تحميل body
            document.addEventListener('DOMContentLoaded', setupDOMObserver);
        }
    }
    
    /**
     * تحديث حالة الإضافة
     */
    function updateExtensionStatus(enabled) {
        const wasEnabled = extensionEnabled;
        extensionEnabled = enabled;
        
        log(`تحديث حالة الإضافة: ${enabled ? 'مفعلة' : 'معطلة'}`);
        
        if (enabled && !wasEnabled) {
            // تم تفعيل الإضافة
            startExtensionFeatures();
        } else if (!enabled && wasEnabled) {
            // تم تعطيل الإضافة
            stopExtensionFeatures();
        }
    }
    
    /**
     * بدء ميزات الإضافة
     */
    function startExtensionFeatures() {
        if (!extensionEnabled) return;
        
        log('بدء ميزات الإضافة...');
        
        // تطبيق الوظائف الأساسية للإضافة
        applyExtensionFeatures();
        
        // بدء المراقبة المستمرة
        startContinuousMonitoring();
    }
    
    /**
     * إيقاف ميزات الإضافة
     */
    function stopExtensionFeatures() {
        log('إيقاف ميزات الإضافة...');
        
        // إيقاف المراقبة
        stopContinuousMonitoring();
        
        // إزالة التأثيرات المطبقة (اختياري)
        removeExtensionEffects();
    }
    
    /**
     * تطبيق الوظائف الأساسية للإضافة
     * يجب تخصيص هذه الدالة حسب نوع الإضافة
     */
    function applyExtensionFeatures() {
        if (!extensionEnabled) return;
        
        // مثال: إخفاء عناصر كاشف مانع الإعلانات
        hideAdBlockDetectors();
        
        // مثال: تطبيق تحسينات على الصفحة
        applyPageEnhancements();
        
        // مثال: حقن CSS مخصص
        injectCustomStyles();
    }
    
    /**
     * إخفاء عناصر كاشف مانع الإعلانات (مثال)
     */
    function hideAdBlockDetectors() {
        const selectors = [
            'div:contains("Ads Blocker Detected")',
            'div:contains("AdBlock Detected")',
            '[class*="adblock"]',
            '[id*="adblock"]',
            'img[src*="chp-ads-block-detector"]'
        ];
        
        selectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    element.style.display = 'none';
                    log(`تم إخفاء عنصر: ${element.tagName}`);
                });
            } catch (error) {
                log(`خطأ في تطبيق selector: ${selector}`, 'error');
            }
        });
        
        // البحث في النصوص
        const textNodes = getTextNodes();
        textNodes.forEach(node => {
            if (node.textContent.includes('Ads Blocker Detected') || 
                node.textContent.includes('AdBlock Detected')) {
                const parent = node.parentElement;
                if (parent) {
                    parent.style.display = 'none';
                    log('تم إخفاء عنصر يحتوي على نص كاشف الإعلانات');
                }
            }
        });
    }
    
    /**
     * تطبيق تحسينات على الصفحة (مثال)
     */
    function applyPageEnhancements() {
        // مثال: إزالة overlay مزعج
        const overlays = document.querySelectorAll('[class*="overlay"], [class*="modal"]');
        overlays.forEach(overlay => {
            if (overlay.style.zIndex > 1000) {
                overlay.style.display = 'none';
            }
        });
    }
    
    /**
     * حقن CSS مخصص (مثال)
     */
    function injectCustomStyles() {
        const styleId = 'cih99-activation-styles';
        
        // تجنب الحقن المتكرر
        if (document.getElementById(styleId)) return;
        
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* أنماط مخصصة لنظام التفعيل */
            [class*="adblock-detector"] {
                display: none !important;
            }
            
            [id*="adblock-warning"] {
                display: none !important;
            }
            
            .cih99-hidden {
                display: none !important;
            }
        `;
        
        document.head.appendChild(style);
        log('تم حقن الأنماط المخصصة');
    }
    
    /**
     * إزالة تأثيرات الإضافة
     */
    function removeExtensionEffects() {
        // إزالة الأنماط المحقونة
        const customStyle = document.getElementById('cih99-activation-styles');
        if (customStyle) {
            customStyle.remove();
            log('تم إزالة الأنماط المخصصة');
        }
        
        // إظهار العناصر المخفية (اختياري)
        showHiddenElements();
    }
    
    /**
     * إظهار العناصر المخفية
     */
    function showHiddenElements() {
        const hiddenElements = document.querySelectorAll('.cih99-hidden');
        hiddenElements.forEach(element => {
            element.classList.remove('cih99-hidden');
            element.style.display = '';
        });
    }
    
    /**
     * إعداد مراقب DOM
     */
    function setupDOMObserver() {
        if (!document.body) return;
        
        const observer = new MutationObserver((mutations) => {
            if (!extensionEnabled) return;
            
            mutations.forEach((mutation) => {
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // تطبيق الميزات على العناصر الجديدة
                    applyExtensionFeatures();
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // حفظ مرجع للمراقب
        window.cih99Observer = observer;
        log('تم إعداد مراقب DOM');
    }
    
    /**
     * بدء المراقبة المستمرة
     */
    function startContinuousMonitoring() {
        if (window.cih99MonitoringInterval) return;
        
        window.cih99MonitoringInterval = setInterval(() => {
            if (extensionEnabled) {
                applyExtensionFeatures();
            }
        }, config.checkInterval);
        
        log('تم بدء المراقبة المستمرة');
    }
    
    /**
     * إيقاف المراقبة المستمرة
     */
    function stopContinuousMonitoring() {
        if (window.cih99MonitoringInterval) {
            clearInterval(window.cih99MonitoringInterval);
            window.cih99MonitoringInterval = null;
            log('تم إيقاف المراقبة المستمرة');
        }
    }
    
    /**
     * الحصول على عقد النص
     */
    function getTextNodes() {
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        const textNodes = [];
        let node;
        
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        
        return textNodes;
    }
    
    /**
     * تنظيف الموارد
     */
    function cleanup() {
        stopContinuousMonitoring();
        
        if (window.cih99Observer) {
            window.cih99Observer.disconnect();
            window.cih99Observer = null;
        }
        
        log('تم تنظيف الموارد');
    }
    
    // بدء التهيئة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // تنظيف عند مغادرة الصفحة
    window.addEventListener('beforeunload', cleanup);
    
    log('تم تحميل content script لنظام التفعيل');
    
})();
