(function() {
    // متغير لتخزين حالة الإضافة (مفعلة/معطلة)
    let extensionEnabled = true;

    // الدالة الرئيسية لإخفاء عناصر كاشف مانع الإعلانات
    function hideAdBlockDetectors() {
        // إذا كانت الإضافة معطلة، لا تقم بأي شيء
        if (!extensionEnabled) return;

        // البحث عن جميع عناصر div في الصفحة
        var divElements = document.querySelectorAll('div');

        // فحص كل عنصر div
        divElements.forEach(function(element) {
            // إذا كان النص يحتوي على "Ads Blocker Detected" أو كان يحتوي على صورة معينة
            if (element.textContent.includes('Ads Blocker Detected') ||
                element.querySelector('img[src="https://fazy.online/wp-content/plugins/chp-ads-block-detector/assets/img/icon.png"]')) {
                // إخفاء العنصر
                element.style.display = 'none';
            }
        });

        // البحث عن عناصر أخرى قد تكون مرتبطة بكاشف مانع الإعلانات
        const adBlockMessages = document.querySelectorAll('.adblock-message, .ad-blocker-notice, .adblock-warning, [class*="adblock"], [id*="adblock"]');
        adBlockMessages.forEach(element => {
            element.style.display = 'none';
        });
    }

    // دالة لإظهار عناصر كاشف مانع الإعلانات (عند تعطيل الإضافة)
    function showAdBlockDetectors() {
        // البحث عن العناصر المخفية وإظهارها
        const hiddenElements = document.querySelectorAll('[data-hidden-by-gurusvip="true"]');
        hiddenElements.forEach(element => {
            element.style.display = element.dataset.originalDisplay || 'block';
            delete element.dataset.hiddenByGurusvip;
            delete element.dataset.originalDisplay;
        });
    }

    // إنشاء مراقب للتغييرات في DOM
    var observer = new MutationObserver(function(mutations) {
        if (!extensionEnabled) return;

        mutations.forEach(function(mutation) {
            // إذا تمت إضافة عناصر جديدة للصفحة
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                // تشغيل دالة إخفاء كاشف مانع الإعلانات
                hideAdBlockDetectors();
            }
        });
    });

    // دالة لبدء المراقبة
    function startObserver() {
        if (document.body) {
            observer.observe(document.body, {
                'childList': true,
                'subtree': true
            });
        }
    }

    // دالة لإيقاف المراقبة
    function stopObserver() {
        observer.disconnect();
    }

    // التحقق من حالة الإضافة عند بدء التشغيل
    chrome.storage.local.get('enabled', function(data) {
        if (data.enabled === false) {
            extensionEnabled = false;
        } else {
            extensionEnabled = true;
            // بدء المراقبة وتشغيل الإخفاء إذا كانت الإضافة مفعلة
            startObserver();
            hideAdBlockDetectors();
        }
    });

    // الاستماع للرسائل من سكربت الخلفية
    chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
        if (message.action === 'updateStatus') {
            extensionEnabled = message.enabled;

            if (extensionEnabled) {
                startObserver();
                hideAdBlockDetectors();
            } else {
                stopObserver();
                showAdBlockDetectors();
            }
        }
    });

    // تشغيل الدالة عند تحميل الصفحة
    window.addEventListener('load', function() {
        if (extensionEnabled) {
            hideAdBlockDetectors();
        }
    });

    // تشغيل الدالة مباشرة في حالة تحميل الصفحة بالفعل
    if (document.readyState === 'complete' && extensionEnabled) {
        hideAdBlockDetectors();
    }
})();
