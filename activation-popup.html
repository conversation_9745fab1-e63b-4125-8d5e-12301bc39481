<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CIH99 Activation System</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      background: linear-gradient(135deg, #1a0000, #330000, #1a0000);
      color: #ffffff;
      width: 350px;
      height: auto;
      min-height: 400px;
      overflow: hidden;
      position: relative;
    }

    .overlay {
      background: rgba(0, 0, 0, 0.8);
      padding: 20px;
      border-radius: 15px;
      text-align: center;
      position: relative;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 0, 0, 0.3);
      box-shadow: 0 0 30px rgba(255, 0, 0, 0.5);
    }

    .title {
      font-size: 28px;
      font-weight: bold;
      color: #ff0000;
      margin-bottom: 10px;
      text-shadow: 0 0 10px rgba(255, 0, 0, 0.8);
      animation: pulse 2s infinite;
    }

    .description {
      font-size: 16px;
      color: #cccccc;
      margin-bottom: 20px;
      font-weight: bold;
    }

    .icon-container {
      margin: 20px 0;
      position: relative;
    }

    .detector-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 3px solid #ff0000;
      box-shadow: 0 0 20px rgba(255, 0, 0, 0.7);
      animation: water-wave 3s linear infinite;
    }

    .switch-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 0;
      gap: 15px;
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #700000;
      transition: .4s;
      border-radius: 34px;
      border: 2px solid #ff0000;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 2px;
      bottom: 2px;
      background-color: #ffffff;
      transition: .4s;
      border-radius: 50%;
      box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
    }

    input:checked + .slider {
      background-color: #cc0000;
      box-shadow: 0 0 15px rgba(255, 0, 0, 0.8);
    }

    input:checked + .slider:before {
      transform: translateX(26px);
      background-color: #ffffff;
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    }

    .status-text {
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
    }

    .password-section {
      margin-top: 20px;
      padding: 15px;
      background-color: rgba(50, 0, 0, 0.5);
      border-radius: 10px;
      border: 1px solid #700000;
    }

    .password-container {
      display: flex;
      margin-bottom: 10px;
    }

    .password-input {
      background-color: #300000;
      border: 1px solid #700000;
      color: #ffffff;
      padding: 8px 12px;
      border-radius: 5px 0 0 5px;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      width: 70%;
      outline: none;
    }

    .password-input::placeholder {
      color: #aa5555;
    }

    .password-button {
      background-color: #cc0000;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 0 5px 5px 0;
      cursor: pointer;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      transition: all 0.3s;
    }

    .password-button:hover {
      background-color: #ff0000;
      box-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
    }

    .password-info {
      font-size: 12px;
      color: #cccccc;
      text-align: center;
      margin-top: 5px;
    }

    .password-info a {
      color: #ff5555;
      text-decoration: none;
      font-weight: bold;
    }

    .password-info a:hover {
      color: #ffffff;
      text-decoration: underline;
    }

    .message-box {
      margin-top: 10px;
      padding: 8px 12px;
      border-radius: 5px;
      background-color: rgba(80, 0, 0, 0.7);
      border: 1px solid #700000;
      color: #ffffff;
      position: relative;
      font-size: 14px;
      text-align: center;
      animation: fadeIn 0.3s ease-in-out;
    }

    .message-box.success {
      background-color: rgba(0, 80, 0, 0.7);
      border: 1px solid #007000;
    }

    .close-button {
      position: absolute;
      top: 5px;
      right: 5px;
      background: none;
      border: none;
      color: #ffffff;
      font-size: 16px;
      cursor: pointer;
      padding: 0 5px;
    }

    .close-button:hover {
      color: #ff5555;
    }

    .footer {
      margin-top: 20px;
      font-size: 12px;
      color: #888888;
    }

    .footer a {
      color: #ff5555;
      text-decoration: none;
      font-weight: bold;
    }

    .footer a:hover {
      color: #ffffff;
      text-decoration: underline;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); text-shadow: 0 0 15px #ff0000, 0 0 25px #ff0000; }
      100% { transform: scale(1); }
    }

    @keyframes water-wave {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="overlay">
    <div class="title">CIH99</div>
    <div class="description" id="descriptionText">نظام التفعيل</div>
    
    <div class="icon-container">
      <img src="https://egysport.online/wp-content/plugins/chp-ads-block-detector/assets/img/icon.png" 
           alt="Extension Icon" class="detector-icon">
    </div>

    <div class="switch-container">
      <span class="status-text" id="statusText">معطل</span>
      <label class="switch">
        <input type="checkbox" id="toggleSwitch">
        <span class="slider"></span>
      </label>
    </div>

    <div id="passwordSection" class="password-section" style="display: none;">
      <div class="password-container">
        <input type="password" id="passwordInput" placeholder="أدخل كلمة المرور" class="password-input">
        <button id="submitPassword" class="password-button">تفعيل</button>
      </div>
      <div class="password-info">
        للحصول على كلمة المرور، يرجى زيارة:
        <a href="#" id="passwordLink">صفحة كلمة المرور</a>
      </div>
      <div id="messageBox" class="message-box" style="display: none;">
        <span id="messageText"></span>
        <button id="closeMessage" class="close-button">×</button>
      </div>
    </div>

    <div class="footer">
      تم تصميم من قبل <a href="#" id="telegramLink">CIH99</a>
    </div>
  </div>

  <script src="activation-ui.js"></script>
  <script>
    // تهيئة واجهة المستخدم عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
      const activationUI = new ActivationUI({
        passwordUrl: 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json',
        telegramLink: 'https://t.me/cih99x',
        passwordPageLink: 'https://example.com/password',
        extensionName: 'CIH99'
      });
    });
  </script>
</body>
</html>
