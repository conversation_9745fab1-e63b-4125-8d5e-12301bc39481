/**
 * واجهة المستخدم لنظام التفعيل - CIH99 Activation UI
 * يمكن استخدامها مع نظام التفعيل في أي مشروع Chrome Extension
 */

class ActivationUI {
    constructor(config = {}) {
        this.config = {
            passwordUrl: config.passwordUrl || 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json',
            telegramLink: config.telegramLink || 'https://t.me/cih99x',
            passwordPageLink: config.passwordPageLink || 'https://example.com/password',
            extensionName: config.extensionName || 'CIH99',
            ...config
        };

        this.elements = {};
        this.init();
    }

    /**
     * تهيئة واجهة المستخدم
     */
    init() {
        this.setupElements();
        this.setupEventListeners();
        this.updateUI();
        this.startPeriodicCheck();
    }

    /**
     * إعداد عناصر واجهة المستخدم
     */
    setupElements() {
        this.elements = {
            toggleSwitch: document.getElementById('toggleSwitch'),
            statusText: document.getElementById('statusText'),
            descriptionText: document.getElementById('descriptionText'),
            passwordSection: document.getElementById('passwordSection'),
            passwordInput: document.getElementById('passwordInput'),
            submitPassword: document.getElementById('submitPassword'),
            messageBox: document.getElementById('messageBox'),
            messageText: document.getElementById('messageText'),
            closeMessage: document.getElementById('closeMessage'),
            passwordLink: document.getElementById('passwordLink'),
            telegramLink: document.getElementById('telegramLink'),
            detectorIcon: document.querySelector('.detector-icon')
        };

        // تعيين الروابط
        if (this.elements.passwordLink) {
            this.elements.passwordLink.href = this.config.passwordPageLink;
            this.elements.passwordLink.target = '_blank';
        }

        if (this.elements.telegramLink) {
            this.elements.telegramLink.href = this.config.telegramLink;
            this.elements.telegramLink.target = '_blank';
        }
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // مفتاح التفعيل/التعطيل
        if (this.elements.toggleSwitch) {
            this.elements.toggleSwitch.addEventListener('change', (e) => {
                this.handleToggleSwitch(e.target.checked);
            });
        }

        // زر إرسال كلمة المرور
        if (this.elements.submitPassword) {
            this.elements.submitPassword.addEventListener('click', () => {
                this.handlePasswordSubmit();
            });
        }

        // إدخال كلمة المرور بالضغط على Enter
        if (this.elements.passwordInput) {
            this.elements.passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handlePasswordSubmit();
                }
            });
        }

        // زر إغلاق الرسالة
        if (this.elements.closeMessage) {
            this.elements.closeMessage.addEventListener('click', () => {
                this.hideMessage();
            });
        }
    }

    /**
     * التعامل مع تغيير مفتاح التفعيل
     */
    async handleToggleSwitch(isChecked) {
        if (isChecked) {
            // محاولة التفعيل
            const status = await this.getSystemStatus();
            
            if (status.passwordRequired && !status.passwordVerified) {
                // إذا كانت كلمة المرور مطلوبة ولم يتم التحقق منها
                this.elements.toggleSwitch.checked = false;
                this.updateStatusText('معطل');
                this.showPasswordSection();
                this.showMessage('يرجى إدخال كلمة المرور لتفعيل الإضافة.', false);
            } else {
                // تفعيل الإضافة
                chrome.runtime.sendMessage({action: 'enable'});
                this.updateStatusText('نشط');
                this.updateIconVisibility(true);
            }
        } else {
            // تعطيل الإضافة
            chrome.runtime.sendMessage({action: 'disable'});
            this.updateStatusText('معطل');
            this.updateIconVisibility(false);
        }
    }

    /**
     * التعامل مع إرسال كلمة المرور
     */
    async handlePasswordSubmit() {
        const password = this.elements.passwordInput.value.trim();
        
        if (!password) {
            this.showMessage('يرجى إدخال كلمة المرور.', false);
            return;
        }

        this.showMessage('جاري التحقق من كلمة المرور...', false);

        try {
            const result = await this.verifyPassword(password);
            
            if (result.success) {
                // كلمة المرور صحيحة
                this.elements.toggleSwitch.checked = true;
                this.updateStatusText('نشط');
                this.updateIconVisibility(true);
                this.showMessage(result.message, true);
                
                // إخفاء قسم كلمة المرور بعد ثانية واحدة
                setTimeout(() => {
                    this.hidePasswordSection();
                }, 1000);
            } else {
                // كلمة المرور غير صحيحة
                this.showMessage(result.message, false);
                this.elements.passwordInput.value = '';
                this.elements.passwordInput.focus();
            }
        } catch (error) {
            console.error('خطأ في التحقق من كلمة المرور:', error);
            this.showMessage('حدث خطأ في التحقق من كلمة المرور.', false);
        }
    }

    /**
     * التحقق من كلمة المرور
     */
    async verifyPassword(password) {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage({
                action: 'verifyPassword',
                password: password
            }, (response) => {
                resolve(response);
            });
        });
    }

    /**
     * الحصول على حالة النظام
     */
    async getSystemStatus() {
        return new Promise((resolve) => {
            chrome.storage.local.get(['enabled', 'passwordRequired', 'passwordVerified'], (data) => {
                resolve({
                    enabled: data.enabled !== false,
                    passwordRequired: data.passwordRequired === true,
                    passwordVerified: data.passwordVerified === true
                });
            });
        });
    }

    /**
     * تحديث واجهة المستخدم
     */
    async updateUI() {
        const status = await this.getSystemStatus();
        
        if (status.enabled && (!status.passwordRequired || status.passwordVerified)) {
            // الإضافة مفعلة
            this.elements.toggleSwitch.checked = true;
            this.updateStatusText('نشط');
            this.updateIconVisibility(true);
            this.hidePasswordSection();
        } else {
            // الإضافة معطلة
            this.elements.toggleSwitch.checked = false;
            this.updateStatusText('معطل');
            this.updateIconVisibility(false);
            
            if (status.passwordRequired && !status.passwordVerified) {
                this.showPasswordSection();
            }
        }
    }

    /**
     * تحديث نص الحالة
     */
    updateStatusText(status) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = status;
        }
    }

    /**
     * تحديث رؤية الأيقونة
     */
    updateIconVisibility(isEnabled) {
        if (this.elements.detectorIcon) {
            this.elements.detectorIcon.style.display = isEnabled ? 'none' : 'block';
        }
        
        if (this.elements.descriptionText) {
            this.elements.descriptionText.textContent = isEnabled ? 
                'الإضافة نشطة' : 'تم اكتشاف مانع الإعلانات';
        }
    }

    /**
     * إظهار قسم كلمة المرور
     */
    showPasswordSection() {
        if (this.elements.passwordSection) {
            this.elements.passwordSection.style.display = 'block';
            if (this.elements.passwordInput) {
                this.elements.passwordInput.focus();
            }
        }
    }

    /**
     * إخفاء قسم كلمة المرور
     */
    hidePasswordSection() {
        if (this.elements.passwordSection) {
            this.elements.passwordSection.style.display = 'none';
        }
        if (this.elements.passwordInput) {
            this.elements.passwordInput.value = '';
        }
    }

    /**
     * عرض رسالة
     */
    showMessage(message, isSuccess = false) {
        if (!this.elements.messageBox || !this.elements.messageText) return;

        this.elements.messageText.textContent = message;
        this.elements.messageBox.className = isSuccess ? 
            'message-box success' : 'message-box';
        this.elements.messageBox.style.display = 'block';

        // إخفاء الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            this.hideMessage();
        }, 5000);
    }

    /**
     * إخفاء الرسالة
     */
    hideMessage() {
        if (this.elements.messageBox) {
            this.elements.messageBox.style.display = 'none';
        }
    }

    /**
     * بدء التحقق الدوري
     */
    startPeriodicCheck() {
        // التحقق كل 5 ثوان
        setInterval(() => {
            this.checkPasswordChange();
        }, 5000);
    }

    /**
     * التحقق من تغيير كلمة المرور
     */
    checkPasswordChange() {
        chrome.runtime.sendMessage({action: 'checkPasswordChange'});
    }

    /**
     * إعادة تعيين واجهة المستخدم
     */
    reset() {
        this.hideMessage();
        this.hidePasswordSection();
        this.updateUI();
    }
}

// تصدير الكلاس للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ActivationUI;
} else if (typeof window !== 'undefined') {
    window.ActivationUI = ActivationUI;
}
