// حالة الإضافة الافتراضية هي "مفعلة"
let extensionEnabled = true;

// حفظ حالة الإضافة في التخزين المحلي
chrome.storage.local.set({ 'enabled': extensionEnabled });

// الاستماع للرسائل من النافذة المنبثقة
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'enable') {
    extensionEnabled = true;
    chrome.storage.local.set({ 'enabled': true });
    updateContentScripts();
  } else if (message.action === 'disable') {
    extensionEnabled = false;
    chrome.storage.local.set({ 'enabled': false });
    updateContentScripts();
  } else if (message.action === 'getStatus') {
    sendResponse({ enabled: extensionEnabled });
  } else if (message.action === 'checkPasswordChange') {
    checkPasswordChange();
    sendResponse({ success: true });
    return true; // يشير إلى أن الرد سيتم إرساله بشكل غير متزامن
  }
  return true; // للسماح بالردود غير المتزامنة
});

// تحديث حالة سكربت المحتوى
function updateContentScripts() {
  // إرسال رسالة إلى جميع علامات التبويب النشطة
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach((tab) => {
      chrome.tabs.sendMessage(tab.id, { action: 'updateStatus', enabled: extensionEnabled })
        .catch(() => {
          // تجاهل الأخطاء في حالة عدم تحميل سكربت المحتوى بعد
        });
    });
  });
}

// دالة للتحقق من تغيير كلمة المرور
function checkPasswordChange() {
  console.log('بدء التحقق من تغيير كلمة المرور...');

  // إضافة معلمة عشوائية لتجنب التخزين المؤقت
  const randomParam = Math.random();
  const url = `https://abdelhalimx5.github.io/cih99-passwords/passwords.json?nocache=${randomParam}`;

  // إرسال طلب للحصول على ملف كلمات المرور
  fetch(url, {
    method: 'GET',
    cache: 'no-cache', // تجنب التخزين المؤقت
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`فشل الطلب: ${response.status} ${response.statusText}`);
      }
      return response.json();
    })
    .then(data => {
      // التحقق من صحة البيانات
      if (!data || typeof data !== 'object') {
        console.error('البيانات المستلمة غير صالحة:', data);
        return;
      }

      // تحويل البيانات إلى سلسلة JSON للمقارنة
      const currentPasswordsJSON = JSON.stringify(data);
      console.log('تم استلام بيانات كلمات المرور الحالية:', data);

      // الحصول على آخر كلمات المرور تم التحقق منها من التخزين المحلي
      chrome.storage.local.get(['lastPasswordsJSON', 'enabled', 'passwordVerified'], function(result) {
        // إذا كان lastPasswordsJSON غير موجود، قم بتعيينه فقط دون تغيير حالة الإضافة
        if (!result.lastPasswordsJSON) {
          console.log('لا توجد بيانات سابقة لكلمات المرور. تعيين البيانات الحالية كأول بيانات.');
          chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON }, function() {
            console.log('تم تعيين بيانات كلمات المرور الأولية في التخزين المحلي');
          });
          return;
        }

        const lastPasswordsJSON = result.lastPasswordsJSON;
        const isEnabled = result.enabled !== false; // افتراضي: true
        const isVerified = result.passwordVerified === true; // افتراضي: false

        console.log('آخر بيانات كلمات المرور تم التحقق منها:', lastPasswordsJSON);
        console.log('حالة الإضافة:', isEnabled ? 'مفعلة' : 'معطلة');
        console.log('حالة التحقق من كلمة المرور:', isVerified ? 'تم التحقق' : 'لم يتم التحقق');
        console.log('هل البيانات متطابقة؟', currentPasswordsJSON === lastPasswordsJSON);

        // التحقق من تغيير كلمات المرور بشكل أكثر دقة
        try {
          const lastPasswords = JSON.parse(lastPasswordsJSON);

          // التحقق من أن كلا البيانات صالحة
          if (!lastPasswords || typeof lastPasswords !== 'object') {
            console.error('البيانات السابقة غير صالحة. تحديث البيانات فقط دون تغيير حالة الإضافة.');
            chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
            return;
          }

          // الحصول على كلمات المرور من كلا البيانات
          const oldPasswords = Object.values(lastPasswords);
          const newPasswords = Object.values(data);

          console.log('كلمات المرور القديمة:', oldPasswords);
          console.log('كلمات المرور الجديدة:', newPasswords);

          // التحقق مما إذا كانت كلمات المرور قد تغيرت فعلاً
          let passwordsChanged = false;

          // التحقق من أن عدد كلمات المرور مختلف
          if (oldPasswords.length !== newPasswords.length) {
            passwordsChanged = true;
            console.log('عدد كلمات المرور تغير.');
          } else {
            // التحقق من كل كلمة مرور
            for (let i = 0; i < newPasswords.length; i++) {
              if (!oldPasswords.includes(newPasswords[i])) {
                passwordsChanged = true;
                console.log(`كلمة المرور الجديدة غير موجودة في القائمة القديمة: ${newPasswords[i]}`);
                break;
              }
            }
          }

          if (passwordsChanged) {
            // تم تغيير كلمات المرور
            console.log('!!! تم اكتشاف تغيير في كلمات المرور !!!');
            console.log('كلمات المرور القديمة:', oldPasswords);
            console.log('كلمات المرور الجديدة:', newPasswords);

            // تحديث آخر بيانات كلمات المرور تم التحقق منها في التخزين المحلي
            chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON }, function() {
              console.log('تم تحديث آخر بيانات كلمات المرور في التخزين المحلي');
            });

            // تعيين حالة كلمة المرور إلى "مطلوبة" وإعادة تعيين حالة التحقق
            chrome.storage.local.set({
              'passwordRequired': true,
              'passwordVerified': false
            }, function() {
              console.log('تم تعيين حالة كلمة المرور إلى "مطلوبة" وإعادة تعيين حالة التحقق');
            });

            // تعطيل الإضافة حتى يتم إدخال كلمة المرور الجديدة
            chrome.storage.local.set({ 'enabled': false }, function() {
              console.log('تم تعطيل الإضافة حتى يتم إدخال كلمة المرور الجديدة');
              extensionEnabled = false;

              // تحديث سكربت المحتوى
              updateContentScripts();
              console.log('تم تحديث سكربت المحتوى');

              // إرسال إشعار للمستخدم
              chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'تم تغيير كلمة المرور',
                message: 'تم تعطيل الإضافة. يرجى إدخال كلمة المرور الجديدة لإعادة تفعيلها.',
                priority: 2
              });
            });
          } else {
            console.log('لم يتم اكتشاف أي تغيير في كلمات المرور');
            // تحديث البيانات فقط للتأكد من أن التنسيق متطابق
            if (currentPasswordsJSON !== lastPasswordsJSON) {
              chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
            }
          }
        } catch (e) {
          console.error('خطأ في تحليل البيانات السابقة:', e);
          // تحديث البيانات فقط دون تغيير حالة الإضافة
          chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
        }
      });
    })
    .catch(error => {
      console.error('خطأ في التحقق من تغيير كلمة المرور:', error);
    });
}

// عند تثبيت الإضافة أو تحديثها
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.get('enabled', (data) => {
    if (data.enabled === undefined) {
      chrome.storage.local.set({ 'enabled': true });
    } else {
      extensionEnabled = data.enabled;
    }
  });

  // تعيين حالة كلمة المرور الأولية
  chrome.storage.local.set({
    'passwordRequired': true,
    'passwordVerified': false
  });

  // التحقق من كلمة المرور الحالية
  checkPasswordChange();
});

// إضافة مستمع للتحقق من تغيير كلمة المرور عند بدء تشغيل المتصفح
chrome.runtime.onStartup.addListener(() => {
  checkPasswordChange();
});

// التحقق من تغيير كلمة المرور عند بدء التشغيل
checkPasswordChange();

// التحقق من تغيير كلمة المرور بشكل دوري (كل دقيقة)
// هذا للتأكد من أن الإضافة ستكتشف التغييرات حتى إذا لم يتم فتح النافذة المنبثقة
setInterval(checkPasswordChange, 60 * 1000);
