/**
 * Background Script لنظام التفعيل - CIH99 Activation System
 * يدير العمليات الخلفية لنظام التفعيل
 */

// استيراد نظام التفعيل
// في بيئة Chrome Extension، نحتاج لتضمين الملف مباشرة
// أو استخدام importScripts في Manifest V2

// متغير لتخزين نظام التفعيل
let activationSystem = null;

// تهيئة نظام التفعيل
function initializeActivationSystem() {
    // إعدادات النظام
    const config = {
        passwordUrl: 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json',
        checkInterval: 30000, // 30 ثانية
        extensionName: 'CIH99'
    };

    // إنشاء نظام التفعيل
    activationSystem = new ActivationSystem(config);
    console.log('تم تهيئة نظام التفعيل في الخلفية');
}

// عند تثبيت الإضافة أو تحديثها
chrome.runtime.onInstalled.addListener((details) => {
    console.log('تم تثبيت/تحديث الإضافة:', details.reason);
    
    // تهيئة نظام التفعيل
    initializeActivationSystem();
    
    // تعيين الحالة الأولية
    chrome.storage.local.get('enabled', (data) => {
        if (data.enabled === undefined) {
            chrome.storage.local.set({ 'enabled': true });
        }
    });
});

// عند بدء تشغيل الإضافة
chrome.runtime.onStartup.addListener(() => {
    console.log('بدء تشغيل الإضافة');
    initializeActivationSystem();
});

// الاستماع للرسائل من النافذة المنبثقة وسكريبت المحتوى
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('تم استلام رسالة:', message);
    
    switch (message.action) {
        case 'enable':
            handleEnableExtension();
            sendResponse({ success: true });
            break;
            
        case 'disable':
            handleDisableExtension();
            sendResponse({ success: true });
            break;
            
        case 'getStatus':
            handleGetStatus(sendResponse);
            return true; // للردود غير المتزامنة
            
        case 'checkPasswordChange':
            handleCheckPasswordChange();
            sendResponse({ success: true });
            break;
            
        case 'verifyPassword':
            handleVerifyPassword(message.password, sendResponse);
            return true; // للردود غير المتزامنة
            
        case 'getSystemStatus':
            handleGetSystemStatus(sendResponse);
            return true; // للردود غير المتزامنة
            
        default:
            console.log('رسالة غير معروفة:', message.action);
            sendResponse({ success: false, error: 'Unknown action' });
    }
    
    return false;
});

// تفعيل الإضافة
function handleEnableExtension() {
    console.log('تفعيل الإضافة...');
    
    chrome.storage.local.set({ 'enabled': true }, () => {
        updateContentScripts(true);
        console.log('تم تفعيل الإضافة');
    });
}

// تعطيل الإضافة
function handleDisableExtension() {
    console.log('تعطيل الإضافة...');
    
    chrome.storage.local.set({ 'enabled': false }, () => {
        updateContentScripts(false);
        console.log('تم تعطيل الإضافة');
    });
}

// الحصول على حالة الإضافة
function handleGetStatus(sendResponse) {
    chrome.storage.local.get('enabled', (data) => {
        sendResponse({ enabled: data.enabled !== false });
    });
}

// التحقق من تغيير كلمة المرور
function handleCheckPasswordChange() {
    console.log('طلب التحقق من تغيير كلمة المرور...');
    
    if (activationSystem) {
        activationSystem.checkPasswordChange();
    } else {
        console.error('نظام التفعيل غير مهيأ');
    }
}

// التحقق من كلمة المرور
async function handleVerifyPassword(password, sendResponse) {
    console.log('طلب التحقق من كلمة المرور...');
    
    if (!activationSystem) {
        sendResponse({
            success: false,
            message: 'نظام التفعيل غير مهيأ'
        });
        return;
    }
    
    try {
        const result = await activationSystem.verifyPassword(password);
        sendResponse(result);
    } catch (error) {
        console.error('خطأ في التحقق من كلمة المرور:', error);
        sendResponse({
            success: false,
            message: 'حدث خطأ في التحقق من كلمة المرور'
        });
    }
}

// الحصول على حالة النظام
async function handleGetSystemStatus(sendResponse) {
    if (!activationSystem) {
        sendResponse({
            enabled: false,
            passwordRequired: true,
            passwordVerified: false,
            extensionEnabled: false
        });
        return;
    }
    
    try {
        const status = await activationSystem.getSystemStatus();
        sendResponse(status);
    } catch (error) {
        console.error('خطأ في الحصول على حالة النظام:', error);
        sendResponse({
            enabled: false,
            passwordRequired: true,
            passwordVerified: false,
            extensionEnabled: false
        });
    }
}

// تحديث سكريبت المحتوى في جميع التابات
function updateContentScripts(enabled) {
    chrome.tabs.query({}, (tabs) => {
        tabs.forEach((tab) => {
            // تجنب التابات الخاصة بـ Chrome
            if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'updateStatus',
                    enabled: enabled
                }).catch((error) => {
                    // تجاهل الأخطاء للتابات التي لا تحتوي على سكريبت المحتوى
                    console.log('لا يمكن إرسال رسالة للتاب:', tab.id, error.message);
                });
            }
        });
    });
}

// مراقبة تغييرات التخزين المحلي
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
        console.log('تغييرات في التخزين المحلي:', changes);
        
        // إذا تم تغيير حالة التفعيل
        if (changes.enabled) {
            updateContentScripts(changes.enabled.newValue);
        }
        
        // إذا تم تغيير حالة كلمة المرور
        if (changes.passwordRequired || changes.passwordVerified) {
            console.log('تم تغيير حالة كلمة المرور');
        }
    }
});

// مراقبة إنشاء تابات جديدة
chrome.tabs.onCreated.addListener((tab) => {
    // إرسال حالة الإضافة للتاب الجديد بعد تحميله
    chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
        if (tabId === tab.id && info.status === 'complete') {
            chrome.storage.local.get('enabled', (data) => {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'updateStatus',
                        enabled: data.enabled !== false
                    }).catch(() => {
                        // تجاهل الأخطاء
                    });
                }
            });
            
            // إزالة المستمع بعد الاستخدام
            chrome.tabs.onUpdated.removeListener(listener);
        }
    });
});

// تنظيف الموارد عند إغلاق الإضافة
chrome.runtime.onSuspend.addListener(() => {
    console.log('إغلاق الإضافة...');
    
    if (activationSystem) {
        activationSystem.destroy();
        activationSystem = null;
    }
});

// معالجة الأخطاء العامة
self.addEventListener('error', (event) => {
    console.error('خطأ في background script:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
    console.error('Promise مرفوض في background script:', event.reason);
});

console.log('تم تحميل background script لنظام التفعيل');
