// تأثيرات متقدمة للإضافة
document.addEventListener('DOMContentLoaded', function() {
  // إنشاء عنصر canvas
  const canvas = document.createElement('canvas');
  canvas.width = 320;
  canvas.height = 280;
  document.body.appendChild(canvas);
  
  const ctx = canvas.getContext('2d');
  
  // إنشاء الجزيئات
  const particles = [];
  const particleCount = 100;
  
  for (let i = 0; i < particleCount; i++) {
    particles.push({
      x: Math.random() * canvas.width,
      y: Math.random() * canvas.height,
      radius: Math.random() * 2 + 1,
      color: `rgba(255, ${Math.floor(Math.random() * 50)}, ${Math.floor(Math.random() * 50)}, ${Math.random() * 0.5 + 0.2})`,
      speedX: Math.random() * 2 - 1,
      speedY: Math.random() * 2 - 1
    });
  }
  
  // إنشاء الدوامة
  const vortexCenter = {
    x: canvas.width / 2,
    y: canvas.height / 2
  };
  
  // حلقة الرسم
  function animate() {
    // مسح الشاشة
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // رسم الدوامة
    drawVortex();
    
    // تحديث وعرض الجزيئات
    updateParticles();
    
    // استمرار الحلقة
    requestAnimationFrame(animate);
  }
  
  function drawVortex() {
    const time = Date.now() * 0.001;
    const radius = 100;
    const segments = 50;
    
    for (let i = 0; i < segments; i++) {
      const angle1 = (i / segments) * Math.PI * 2;
      const angle2 = ((i + 1) / segments) * Math.PI * 2;
      
      const wave = Math.sin(time * 2) * 5;
      
      const innerRadius = 20 + Math.sin(time + i * 0.1) * 10;
      const outerRadius = radius + Math.cos(time * 0.7 + i * 0.05) * wave;
      
      const x1 = vortexCenter.x + Math.cos(angle1) * innerRadius;
      const y1 = vortexCenter.y + Math.sin(angle1) * innerRadius;
      
      const x2 = vortexCenter.x + Math.cos(angle2) * innerRadius;
      const y2 = vortexCenter.y + Math.sin(angle2) * innerRadius;
      
      const x3 = vortexCenter.x + Math.cos(angle2) * outerRadius;
      const y3 = vortexCenter.y + Math.sin(angle2) * outerRadius;
      
      const x4 = vortexCenter.x + Math.cos(angle1) * outerRadius;
      const y4 = vortexCenter.y + Math.sin(angle1) * outerRadius;
      
      const gradient = ctx.createRadialGradient(
        vortexCenter.x, vortexCenter.y, innerRadius,
        vortexCenter.x, vortexCenter.y, outerRadius
      );
      
      gradient.addColorStop(0, 'rgba(100, 0, 0, 0.5)');
      gradient.addColorStop(1, 'rgba(50, 0, 0, 0)');
      
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.lineTo(x3, y3);
      ctx.lineTo(x4, y4);
      ctx.closePath();
      
      ctx.fillStyle = gradient;
      ctx.fill();
    }
  }
  
  function updateParticles() {
    for (let i = 0; i < particles.length; i++) {
      const p = particles[i];
      
      // حساب المسافة من مركز الدوامة
      const dx = p.x - vortexCenter.x;
      const dy = p.y - vortexCenter.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      // تأثير الدوامة
      const angle = Math.atan2(dy, dx);
      const vortexForce = 0.5 / (distance * 0.1);
      
      p.speedX += Math.cos(angle + Math.PI/2) * vortexForce;
      p.speedY += Math.sin(angle + Math.PI/2) * vortexForce;
      
      // تحديث الموقع
      p.x += p.speedX;
      p.y += p.speedY;
      
      // إبقاء الجزيئات داخل الشاشة
      if (p.x < 0 || p.x > canvas.width || p.y < 0 || p.y > canvas.height) {
        p.x = Math.random() * canvas.width;
        p.y = Math.random() * canvas.height;
        p.speedX = Math.random() * 2 - 1;
        p.speedY = Math.random() * 2 - 1;
      }
      
      // رسم الجزيئة
      ctx.beginPath();
      ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
      ctx.fillStyle = p.color;
      ctx.fill();
    }
  }
  
  // بدء الرسوم المتحركة
  animate();
});
