<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CIH99</title>
  <style>
    body {
      width: 320px;
      height: 280px;
      background-color: #000000;
      color: #ffffff;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      margin: 0;
      padding: 0;
      overflow: hidden;
      position: relative;
    }

    canvas {
      display: block;
      position: absolute;
      top: 0;
      left: 0;
    }

    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      box-sizing: border-box;
      z-index: 10;
    }

    .title {
      font-size: 36px;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      margin-bottom: 10px;
      text-shadow: 0 0 10px #ff0000, 0 0 20px #ff0000, 0 0 30px #ff0000;
      text-align: center;
      animation: pulse 2s infinite;
      letter-spacing: 2px;
      color: #ffffff;
    }

    .description {
      font-size: 16px;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
      text-shadow: 1px 1px 3px #000000;
    }

    /* تأثير حركي للنص */
    .text-glow {
      animation: textGlow 2s infinite alternate;
      color: #ff3333;
    }

    @keyframes textGlow {
      0% {
        text-shadow: 0 0 5px #ff0000;
        color: #ff3333;
      }
      50% {
        text-shadow: 0 0 15px #ff0000, 0 0 25px #ff0000;
        color: #ff5555;
      }
      100% {
        text-shadow: 0 0 5px #ff0000;
        color: #ff3333;
      }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); text-shadow: 0 0 15px #ff0000, 0 0 25px #ff0000; }
      100% { transform: scale(1); }
    }

    .toggle-container {
      margin-top: 20px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 30px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #500000;
      transition: .4s;
      border-radius: 30px;
      border: 2px solid #700000;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 22px;
      width: 22px;
      left: 4px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
    }

    input:checked + .slider {
      background-color: #cc0000;
      box-shadow: 0 0 15px #ff0000;
    }

    input:checked + .slider:before {
      transform: translateX(30px);
    }

    .status-text {
      margin-left: 10px;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      text-shadow: 0 0 5px #ff0000;
    }

    .footer {
      text-align: center;
      font-size: 12px;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      color: #ffcccc;
      text-shadow: 0 0 3px #800000;
      z-index: 10;
      margin-top: 15px;
    }

    .footer a {
      color: #ffcc00;
      text-decoration: none;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      text-shadow: 0 0 5px #cc0000;
      transition: all 0.3s ease;
    }

    .footer a:hover {
      color: #ffffff;
      text-shadow: 0 0 8px #ff0000;
    }

    .icon-container {
      display: flex;
      justify-content: center;
      margin-bottom: 15px;
    }

    .detector-icon {
      width: 40px;
      height: 40px;
      filter: drop-shadow(0 0 5px rgba(255, 0, 0, 0.7));
      animation: pulse 2s infinite;
    }

    .password-section {
      margin-top: 15px;
      padding: 10px;
      border-top: 1px solid #500000;
      border-bottom: 1px solid #500000;
      background-color: rgba(80, 0, 0, 0.2);
    }

    .password-container {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;
    }

    .password-input {
      background-color: #300000;
      border: 1px solid #700000;
      color: #ffffff;
      padding: 8px 12px;
      border-radius: 5px 0 0 5px;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      width: 70%;
      outline: none;
    }

    .password-input::placeholder {
      color: #aa5555;
    }

    .password-button {
      background-color: #cc0000;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 0 5px 5px 0;
      cursor: pointer;
      font-family: 'Calibri', 'Calibri Bold', sans-serif;
      font-weight: bold;
      transition: all 0.3s;
    }

    .password-button:hover {
      background-color: #ff0000;
      box-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
    }

    .password-info {
      font-size: 12px;
      color: #cccccc;
      text-align: center;
      margin-top: 5px;
    }

    .password-info a {
      color: #ff5555;
      text-decoration: none;
      font-weight: bold;
    }

    .password-info a:hover {
      color: #ffffff;
      text-decoration: underline;
    }

    .message-box {
      margin-top: 10px;
      padding: 8px 12px;
      border-radius: 5px;
      background-color: rgba(80, 0, 0, 0.7);
      border: 1px solid #700000;
      color: #ffffff;
      position: relative;
      font-size: 14px;
      text-align: center;
      animation: fadeIn 0.3s ease-in-out;
    }

    .message-box.success {
      background-color: rgba(0, 80, 0, 0.7);
      border: 1px solid #007000;
    }

    .close-button {
      position: absolute;
      top: 5px;
      right: 5px;
      background: none;
      border: none;
      color: #ffffff;
      font-size: 16px;
      cursor: pointer;
      padding: 0 5px;
    }

    .close-button:hover {
      color: #ff5555;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }



    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); text-shadow: 0 0 15px #ff0000, 0 0 25px #ff0000; }
      100% { transform: scale(1); }
    }







    @keyframes water-wave {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="overlay">
    <div class="title">CIH99</div>
    <div class="description" id="descriptionText">تم اكتشاف مانع الإعلانات</div>
    <div class="icon-container">
      <img src="https://egysport.online/wp-content/plugins/chp-ads-block-detector/assets/img/icon.png" alt="Ad Blocker Icon" class="detector-icon">
    </div>

    <div class="toggle-container">
      <label class="toggle-switch">
        <input type="checkbox" id="toggleSwitch" checked>
        <span class="slider"></span>
      </label>
      <span class="status-text" id="statusText">نشط</span>
    </div>

    <div id="passwordSection" class="password-section" style="display: none;">
      <div class="password-container">
        <input type="password" id="passwordInput" placeholder="أدخل كلمة المرور" class="password-input">
        <button id="submitPassword" class="password-button">تفعيل</button>
      </div>
      <div class="password-info">
        للحصول على كلمة المرور، يرجى زيارة:
        <a href="#" id="passwordLink">صفحة كلمة المرور</a>
      </div>
      <div id="messageBox" class="message-box" style="display: none;">
        <span id="messageText"></span>
        <button id="closeMessage" class="close-button">×</button>
      </div>
    </div>

    <div class="footer">
      تم تصميم من قبل <a href="#" id="telegramLink">CIH99</a>
    </div>
  </div>

  <script src="three.js"></script>
  <script src="effects.js"></script>
  <script src="popup.js"></script>
</body>
</html>
