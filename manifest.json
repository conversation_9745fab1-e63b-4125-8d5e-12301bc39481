{"action": {"default_icon": {"128": "icons/icon128.png", "16": "icons/icon16.png", "48": "icons/icon48.png"}, "default_popup": "popup.html"}, "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"exclude_matches": ["https://chatgpt.com/*", "https://www.youtube.com/*", "https://www.udemy.com/*", "https://www.facebook.com/*", "https://twitter.com/*", "https://x.com/*", "https://www.x.com/*", "https://www.instagram.com/*", "https://instagram.com/*", "https://www.linkedin.com/*", "https://github.com/*", "https://stackoverflow.com/*", "https://www.google.com/*", "https://mail.google.com/*", "https://drive.google.com/*", "https://docs.google.com/*", "https://www.netflix.com/*", "https://www.amazon.com/*", "https://www.microsoft.com/*", "https://outlook.live.com/*", "https://outlook.office.com/*", "https://www.office.com/*", "https://www.apple.com/*", "https://www.icloud.com/*", "https://www.reddit.com/*", "https://discord.com/*", "https://discord.gg/*", "https://web.whatsapp.com/*", "https://www.twitch.tv/*", "https://www.coursera.org/*", "https://www.edx.org/*", "https://www.khanacademy.org/*", "https://www.duolingo.com/*", "https://www.quora.com/*", "https://www.pinterest.com/*", "https://www.tiktok.com/*", "https://www.spotify.com/*", "https://www.dropbox.com/*", "https://www.paypal.com/*", "https://www.yahoo.com/*", "https://mail.yahoo.com/*", "https://www.bing.com/*", "https://www.baidu.com/*", "https://www.wikipedia.org/*", "https://en.wikipedia.org/*", "https://ar.wikipedia.org/*", "https://www.nytimes.com/*", "https://www.bbc.com/*", "https://www.bbc.co.uk/*", "https://news.google.com/*", "https://www.cnn.com/*", "https://www.aljazeera.com/*", "https://www.aljazeera.net/*", "https://www.skype.com/*", "https://web.skype.com/*", "https://zoom.us/*", "https://www.zoom.us/*", "https://meet.google.com/*", "https://teams.microsoft.com/*", "https://www.notion.so/*", "https://trello.com/*", "https://www.trello.com/*", "https://asana.com/*", "https://www.asana.com/*", "https://slack.com/*", "https://app.slack.com/*", "https://www.figma.com/*", "https://www.canva.com/*", "https://www.adobe.com/*", "https://www.photoshop.com/*", "https://www.behance.net/*", "https://dribbble.com/*", "https://www.dribbble.com/*", "https://medium.com/*", "https://www.medium.com/*", "https://dev.to/*", "https://www.dev.to/*", "https://www.codecademy.com/*", "https://www.freecodecamp.org/*", "https://leetcode.com/*", "https://www.leetcode.com/*", "https://hackerrank.com/*", "https://www.hackerrank.com/*", "https://www.hackerearth.com/*", "https://codepen.io/*", "https://www.codepen.io/*", "https://jsfiddle.net/*", "https://www.jsfiddle.net/*", "https://replit.com/*", "https://www.replit.com/*", "https://codesandbox.io/*", "https://www.codesandbox.io/*", "https://www.booking.com/*", "https://www.airbnb.com/*", "https://www.tripadvisor.com/*", "https://www.expedia.com/*", "https://www.hotels.com/*", "https://www.trivago.com/*", "https://www.kayak.com/*", "https://www.skyscanner.com/*", "https://www.skyscanner.net/*", "https://www.uber.com/*", "https://www.lyft.com/*", "https://www.careem.com/*", "https://www.ebay.com/*", "https://www.aliexpress.com/*", "https://www.walmart.com/*", "https://www.target.com/*", "https://www.bestbuy.com/*", "https://www.newegg.com/*", "https://www.etsy.com/*", "https://www.shopify.com/*", "https://www.wix.com/*", "https://www.squarespace.com/*", "https://www.wordpress.com/*", "https://wordpress.org/*", "https://www.blogger.com/*", "https://www.tumblr.com/*", "https://www.flickr.com/*", "https://500px.com/*", "https://www.500px.com/*", "https://unsplash.com/*", "https://www.unsplash.com/*", "https://www.pexels.com/*", "https://pixabay.com/*", "https://www.pixabay.com/*", "https://www.shutterstock.com/*", "https://www.gettyimages.com/*", "https://www.imdb.com/*", "https://www.rottentomatoes.com/*", "https://www.metacritic.com/*", "https://www.hulu.com/*", "https://www.disneyplus.com/*", "https://www.hbomax.com/*", "https://www.primevideo.com/*", "https://www.crunchyroll.com/*", "https://www.funimation.com/*", "https://www.dailymotion.com/*", "https://vimeo.com/*", "https://www.vimeo.com/*", "https://soundcloud.com/*", "https://www.soundcloud.com/*", "https://www.deezer.com/*", "https://www.pandora.com/*", "https://www.tidal.com/*", "https://www.apple.com/music/*", "https://music.apple.com/*", "https://www.shazam.com/*", "https://www.last.fm/*", "https://www.goodreads.com/*", "https://www.audible.com/*", "https://www.scribd.com/*", "https://www.wattpad.com/*", "https://www.academia.edu/*", "https://www.researchgate.net/*", "https://www.jstor.org/*", "https://www.webofscience.com/*", "https://scholar.google.com/*", "https://www.mendeley.com/*", "https://www.zotero.org/*", "https://www.overleaf.com/*", "https://www.grammarly.com/*", "https://translate.google.com/*", "https://www.deepl.com/*", "https://www.reverso.net/*", "https://www.duolingo.com/*", "https://www.memrise.com/*", "https://www.babbel.com/*", "https://www.rosettastone.com/*", "https://www.busuu.com/*", "https://www.lingoda.com/*", "https://www.italki.com/*", "https://www.tandem.net/*", "https://www.hellotalk.com/*", "https://www.cambly.com/*", "https://www.preply.com/*", "https://www.verbling.com/*", "https://www.chegg.com/*", "https://www.coursehero.com/*", "https://www.quizlet.com/*", "https://www.brainly.com/*", "https://www.symbolab.com/*", "https://www.wolframalpha.com/*", "https://www.mathway.com/*", "https://www.desmos.com/*", "https://www.geogebra.org/*", "https://www.khanacademy.org/*", "https://www.brilliant.org/*", "https://www.udacity.com/*", "https://www.pluralsight.com/*", "https://www.skillshare.com/*", "https://www.lynda.com/*", "https://www.linkedin.com/learning/*", "https://www.masterclass.com/*", "https://www.futurelearn.com/*", "https://www.datacamp.com/*", "https://www.sololearn.com/*", "https://www.w3schools.com/*", "https://www.codecademy.com/*", "https://www.freecodecamp.org/*", "https://www.geeksforgeeks.org/*", "https://www.tutorialspoint.com/*", "https://www.javatpoint.com/*", "https://www.programiz.com/*", "https://www.hackerrank.com/*", "https://www.leetcode.com/*", "https://www.codewars.com/*", "https://www.topcoder.com/*", "https://www.kaggle.com/*", "https://www.tensorflow.org/*", "https://pytorch.org/*", "https://www.anaconda.com/*", "https://jupyter.org/*", "https://colab.research.google.com/*", "https://www.tableau.com/*", "https://powerbi.microsoft.com/*", "https://www.metabase.com/*", "https://www.looker.com/*", "https://www.qlik.com/*", "https://www.salesforce.com/*", "https://www.hubspot.com/*", "https://www.zendesk.com/*", "https://www.freshworks.com/*", "https://www.zoho.com/*", "https://www.monday.com/*", "https://www.clickup.com/*", "https://www.notion.so/*", "https://evernote.com/*", "https://www.evernote.com/*", "https://www.onenote.com/*", "https://keep.google.com/*", "https://todoist.com/*", "https://www.todoist.com/*", "https://www.anydo.com/*", "https://www.wunderlist.com/*", "https://www.rememberthemilk.com/*", "https://habitica.com/*", "https://www.habitica.com/*", "https://www.strava.com/*", "https://www.fitbit.com/*", "https://www.myfitnesspal.com/*", "https://www.nike.com/*", "https://www.adidas.com/*", "https://www.underarmour.com/*", "https://www.puma.com/*", "https://www.reebok.com/*", "https://www.newbalance.com/*", "https://www.asics.com/*", "https://www.saucony.com/*", "https://www.brooks.com/*", "https://www.hoka.com/*", "https://www.mizuno.com/*", "https://www.salomon.com/*", "https://www.thenorthface.com/*", "https://www.patagonia.com/*", "https://www.columbia.com/*", "https://www.rei.com/*", "https://www.dickssportinggoods.com/*", "https://www.cabelas.com/*", "https://www.basspro.com/*", "https://www.academy.com/*", "https://www.decathlon.com/*", "https://www.footlocker.com/*", "https://www.finishline.com/*", "https://www.jdsports.com/*", "https://www.eastbay.com/*", "https://www.champssports.com/*", "https://www.zappos.com/*", "https://www.dsw.com/*", "https://www.footaction.com/*", "https://www.famousfootwear.com/*", "https://www.journeys.com/*", "https://www.stevemadden.com/*", "https://www.vans.com/*", "https://www.converse.com/*", "https://www.crocs.com/*", "https://www.timberland.com/*", "https://www.ugg.com/*", "https://www.drschollsshoes.com/*", "https://www.clarksusa.com/*", "https://www.ecco.com/*", "https://www.merrell.com/*", "https://www.keenfootwear.com/*", "https://www.teva.com/*", "https://www.chacos.com/*", "https://www.birkenstock.com/*", "https://www.allbirds.com/*", "https://www.rothys.com/*", "https://www.colehaan.com/*", "https://www.allenedmonds.com/*", "https://www.johnstonmurphy.com/*", "https://www.florsheim.com/*", "https://www.rockport.com/*", "https://www.sperry.com/*", "https://www.sebago.com/*", "https://www.dockers.com/*", "https://www.wolverine.com/*", "https://www.redwingshoes.com/*", "https://www.drmartens.com/*", "https://www.catfootwear.com/*", "https://www.palladiumboots.com/*", "https://www.sorel.com/*", "https://www.hunterboots.com/*", "https://www.muckbootcompany.com/*", "https://www.bogs.com/*", "https://www.kamik.com/*", "https://www.lacrossefootwear.com/*", "https://www.mephisto.com/*", "https://www.dansko.com/*", "https://www.alegriashoes.com/*", "https://www.sanita.com/*", "https://www.vionic.com/*", "https://www.orthofeet.com/*", "https://www.propetusa.com/*", "https://www.newbalance.com/*", "https://www.brooksrunning.com/*", "https://www.hokaoneone.com/*", "https://www.on-running.com/*", "https://www.altra.com/*", "https://www.saucony.com/*", "https://www.mizunousa.com/*", "https://www.asics.com/*", "https://www.nike.com/*", "https://www.adidas.com/*", "https://www.underarmour.com/*", "https://www.puma.com/*", "https://www.reebok.com/*", "https://www.newbalance.com/*", "https://www.asics.com/*", "https://www.saucony.com/*", "https://www.brooks.com/*", "https://www.hoka.com/*", "https://www.mizuno.com/*", "https://www.salomon.com/*", "https://3isk.biz/*", "https://aradrmtv.com/*", "https://tuk.tuktuksu1.cfd/*", "https://www.faselhds.care/*", "https://web2.topcinema.cam/*", "https://besto.egybest.land/*", "https://cimalina.live/*"], "js": ["content.js"], "matches": ["https://fazy.online/*", "https://qyix.online/*", "https://ceqit.online/*", "https://ceqit.com/*", "https://trendzs.online/*", "https://www.ceqit.com/*", "https://lifzy.online/*", "https://foldrecipes.com/*", "*://*/*"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}, "description": "CIH99 - تم اكتشاف مانع الإعلانات", "host_permissions": ["https://fazy.online/*", "https://qyix.online/*", "https://ceqit.online/*", "https://ceqit.com/*", "https://trendzs.online/*", "https://www.ceqit.com/*", "https://lifzy.online/*", "https://foldrecipes.com/*", "*://*/*"], "icons": {"128": "icons/icon128.png", "16": "icons/icon16.png", "48": "icons/icon48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu9zPMk7O/S/1+Nex41dzP/trEVmzO72uSeJslR7T0bHJW4X1e+xA/PSpEl6F2Mbkrjgxgb6DTJIMOnSUGjDc8Gdg+upOuqI4NuVznR4/L8vfoxszokVCfGMr7hE9UWawqt2C3mZaR2luP9W+ISpGKm+vtgik3PGkidoQl+P0wkEJqnOItke2RTY42Pr8pNE5wuf1FSfIipw/SDas7Ri/KQRh+0S2wNXlyCkZNaTKLtzCNorByY+uhwXyOPt5o+RuTKr1jKYyCis9DoEa7gBsE0iEMPVqU/y3pwfofYTbalOlCcqoJXDdXdHdUiM8zh/KA/at4lbrRiVqIsMmbAv8jwIDAQAB", "manifest_version": 3, "name": "CIH99", "permissions": ["storage", "tabs", "notifications"], "version": "0.1"}