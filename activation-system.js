/**
 * نظام التفعيل الكامل - CIH99 Activation System
 * يمكن استخدامه في أي مشروع Chrome Extension
 * 
 * المميزات:
 * - التحقق من كلمة المرور عبر الإنترنت
 * - مراقبة تغيير كلمات المرور
 * - إدارة حالة التفعيل
 * - واجهة مستخدم للتفعيل
 * - نظام إشعارات
 */

class ActivationSystem {
    constructor(config = {}) {
        // إعدادات النظام
        this.config = {
            passwordUrl: config.passwordUrl || 'https://abdelhalimx5.github.io/cih99-passwords/passwords.json',
            checkInterval: config.checkInterval || 30000, // 30 ثانية
            extensionName: config.extensionName || 'CIH99',
            ...config
        };
        
        // حالة النظام
        this.extensionEnabled = true;
        this.checkTimer = null;
        
        // تهيئة النظام
        this.init();
    }

    /**
     * تهيئة نظام التفعيل
     */
    init() {
        console.log('تهيئة نظام التفعيل...');
        
        // تعيين الحالة الأولية
        this.setInitialState();
        
        // بدء مراقبة تغيير كلمات المرور
        this.startPasswordMonitoring();
        
        // الاستماع للرسائل
        this.setupMessageListeners();
    }

    /**
     * تعيين الحالة الأولية للنظام
     */
    setInitialState() {
        chrome.storage.local.get('enabled', (data) => {
            if (data.enabled === undefined) {
                chrome.storage.local.set({ 'enabled': true });
            } else {
                this.extensionEnabled = data.enabled;
            }
        });

        // تعيين حالة كلمة المرور الأولية
        chrome.storage.local.set({
            'passwordRequired': true,
            'passwordVerified': false
        });

        // التحقق من كلمة المرور الحالية
        this.checkPasswordChange();
    }

    /**
     * بدء مراقبة تغيير كلمات المرور
     */
    startPasswordMonitoring() {
        // التحقق الفوري
        this.checkPasswordChange();
        
        // التحقق الدوري
        this.checkTimer = setInterval(() => {
            this.checkPasswordChange();
        }, this.config.checkInterval);
    }

    /**
     * إيقاف مراقبة كلمات المرور
     */
    stopPasswordMonitoring() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
    }

    /**
     * إعداد مستمعي الرسائل
     */
    setupMessageListeners() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            switch (message.action) {
                case 'enable':
                    this.enableExtension();
                    break;
                case 'disable':
                    this.disableExtension();
                    break;
                case 'getStatus':
                    sendResponse({ enabled: this.extensionEnabled });
                    break;
                case 'checkPasswordChange':
                    this.checkPasswordChange();
                    sendResponse({ success: true });
                    break;
                case 'verifyPassword':
                    this.verifyPassword(message.password).then(result => {
                        sendResponse(result);
                    });
                    return true; // للردود غير المتزامنة
            }
            return true;
        });
    }

    /**
     * تفعيل الإضافة
     */
    enableExtension() {
        this.extensionEnabled = true;
        chrome.storage.local.set({ 'enabled': true });
        this.updateContentScripts();
        console.log('تم تفعيل الإضافة');
    }

    /**
     * تعطيل الإضافة
     */
    disableExtension() {
        this.extensionEnabled = false;
        chrome.storage.local.set({ 'enabled': false });
        this.updateContentScripts();
        console.log('تم تعطيل الإضافة');
    }

    /**
     * تحديث سكريبت المحتوى
     */
    updateContentScripts() {
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach((tab) => {
                if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'updateStatus',
                        enabled: this.extensionEnabled
                    }).catch(() => {
                        // تجاهل الأخطاء للتابات التي لا تحتوي على سكريبت المحتوى
                    });
                }
            });
        });
    }

    /**
     * التحقق من تغيير كلمة المرور
     */
    async checkPasswordChange() {
        console.log('بدء التحقق من تغيير كلمة المرور...');

        try {
            // إضافة معلمة عشوائية لتجنب التخزين المؤقت
            const randomParam = Math.random();
            const url = `${this.config.passwordUrl}?nocache=${randomParam}`;

            const response = await fetch(url, {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (!response.ok) {
                throw new Error(`فشل الطلب: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const currentPasswordsJSON = JSON.stringify(data);

            // الحصول على البيانات المحفوظة
            const result = await new Promise(resolve => {
                chrome.storage.local.get(['lastPasswordsJSON', 'enabled', 'passwordVerified'], resolve);
            });

            const lastPasswordsJSON = result.lastPasswordsJSON;
            const isEnabled = result.enabled !== false;
            const isVerified = result.passwordVerified === true;

            console.log('حالة الإضافة:', isEnabled ? 'مفعلة' : 'معطلة');
            console.log('حالة التحقق من كلمة المرور:', isVerified ? 'تم التحقق' : 'لم يتم التحقق');

            if (lastPasswordsJSON && lastPasswordsJSON !== currentPasswordsJSON) {
                // تم تغيير كلمات المرور
                console.log('!!! تم اكتشاف تغيير في كلمات المرور !!!');
                
                await this.handlePasswordChange(currentPasswordsJSON);
            } else {
                // لم يتم تغيير كلمات المرور
                console.log('لم يتم اكتشاف أي تغيير في كلمات المرور');
                
                // تحديث البيانات للتأكد من التطابق
                if (currentPasswordsJSON !== lastPasswordsJSON) {
                    chrome.storage.local.set({ 'lastPasswordsJSON': currentPasswordsJSON });
                }
            }

        } catch (error) {
            console.error('خطأ في التحقق من تغيير كلمة المرور:', error);
        }
    }

    /**
     * التعامل مع تغيير كلمة المرور
     */
    async handlePasswordChange(newPasswordsJSON) {
        // تحديث البيانات المحفوظة
        chrome.storage.local.set({ 'lastPasswordsJSON': newPasswordsJSON });

        // تعيين حالة كلمة المرور إلى "مطلوبة"
        chrome.storage.local.set({
            'passwordRequired': true,
            'passwordVerified': false,
            'enabled': false
        });

        // تعطيل الإضافة
        this.extensionEnabled = false;
        this.updateContentScripts();

        // إرسال إشعار للمستخدم
        this.showNotification(
            'تم تغيير كلمة المرور',
            'تم تعطيل الإضافة. يرجى إدخال كلمة المرور الجديدة لإعادة تفعيلها.'
        );

        console.log('تم تعطيل الإضافة بسبب تغيير كلمة المرور');
    }

    /**
     * التحقق من صحة كلمة المرور
     */
    async verifyPassword(password) {
        console.log('التحقق من كلمة المرور...');

        try {
            // إضافة معلمة عشوائية لتجنب التخزين المؤقت
            const randomParam = Math.random();
            const url = `${this.config.passwordUrl}?nocache=${randomParam}`;

            const response = await fetch(url, {
                method: 'GET',
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (!response.ok) {
                throw new Error(`فشل الطلب: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            
            // استخراج جميع كلمات المرور
            let allPasswords = [];
            
            if (data.passwords && Array.isArray(data.passwords)) {
                allPasswords = [...data.passwords];
            }
            
            if (data.additionalPasswords && Array.isArray(data.additionalPasswords)) {
                allPasswords = [...allPasswords, ...data.additionalPasswords];
            }
            
            if (data.specialPasswords && Array.isArray(data.specialPasswords)) {
                allPasswords = [...allPasswords, ...data.specialPasswords];
            }

            // التحقق من كلمة المرور
            if (allPasswords.includes(password)) {
                console.log('كلمة المرور صحيحة!');
                
                // تحديث حالة التحقق
                await new Promise(resolve => {
                    chrome.storage.local.set({
                        passwordVerified: true,
                        passwordRequired: false,
                        enabled: true
                    }, resolve);
                });

                // تفعيل الإضافة
                this.enableExtension();

                return {
                    success: true,
                    message: 'تم تفعيل الإضافة بنجاح!'
                };
            } else {
                console.log('كلمة المرور غير صحيحة');
                return {
                    success: false,
                    message: 'كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.'
                };
            }

        } catch (error) {
            console.error('خطأ في التحقق من كلمة المرور:', error);
            return {
                success: false,
                message: 'حدث خطأ في التحقق من كلمة المرور. يرجى المحاولة لاحقاً.'
            };
        }
    }

    /**
     * عرض إشعار للمستخدم
     */
    showNotification(title, message) {
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: title,
                message: message,
                priority: 2
            });
        }
    }

    /**
     * الحصول على حالة النظام
     */
    async getSystemStatus() {
        return new Promise(resolve => {
            chrome.storage.local.get(['enabled', 'passwordRequired', 'passwordVerified'], (data) => {
                resolve({
                    enabled: data.enabled !== false,
                    passwordRequired: data.passwordRequired === true,
                    passwordVerified: data.passwordVerified === true,
                    extensionEnabled: this.extensionEnabled
                });
            });
        });
    }

    /**
     * إعادة تعيين النظام
     */
    reset() {
        this.stopPasswordMonitoring();
        chrome.storage.local.clear();
        this.setInitialState();
        this.startPasswordMonitoring();
        console.log('تم إعادة تعيين نظام التفعيل');
    }

    /**
     * تدمير النظام
     */
    destroy() {
        this.stopPasswordMonitoring();
        console.log('تم تدمير نظام التفعيل');
    }
}

// تصدير الكلاس للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ActivationSystem;
} else if (typeof window !== 'undefined') {
    window.ActivationSystem = ActivationSystem;
}
