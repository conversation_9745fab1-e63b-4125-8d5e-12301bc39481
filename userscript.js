// ==UserScript==
// @name         CIH99 - مانع كاشف حجب الإعلانات
// @namespace    http://cih99.com/
// @version      0.1
// @description  مانع كاشف حجب الإعلانات
// <AUTHOR>
// @match        https://fazy.online/*
// @match        https://qyix.online/*
// @match        https://ceqit.online/*
// @match        https://ceqit.com/*
// @match        https://trendzs.online/*
// @match        https://www.ceqit.com/*
// @match        https://lifzy.online/*
// @match        https://foldrecipes.com/*
// @match        *://*/*
// @exclude      https://chatgpt.com/*
// @exclude      https://www.youtube.com/*
// @exclude      https://www.udemy.com/*
// @exclude      https://www.facebook.com/*
// @exclude      https://twitter.com/*
// @exclude      https://www.instagram.com/*
// @exclude      https://www.linkedin.com/*
// @exclude      https://github.com/*
// @exclude      https://stackoverflow.com/*
// @exclude      https://www.google.com/*
// @exclude      https://mail.google.com/*
// @exclude      https://drive.google.com/*
// @exclude      https://docs.google.com/*
// @exclude      https://www.netflix.com/*
// @exclude      https://www.amazon.com/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // متغير لتخزين حالة الإضافة (مفعلة/معطلة)
    let extensionEnabled = true;

    // الدالة الرئيسية لإخفاء عناصر كاشف مانع الإعلانات
    function hideAdBlockDetectors() {
        // إذا كانت الإضافة معطلة، لا تقم بأي شيء
        if (!extensionEnabled) return;

        // البحث عن جميع عناصر div في الصفحة
        var divElements = document.querySelectorAll('div');

        // فحص كل عنصر div
        divElements.forEach(function(element) {
            // إذا كان النص يحتوي على "Ads Blocker Detected" أو كان يحتوي على صورة معينة
            if (element.textContent.includes('Ads Blocker Detected') ||
                element.querySelector('img[src="https://fazy.online/wp-content/plugins/chp-ads-block-detector/assets/img/icon.png"]')) {
                // إخفاء العنصر
                element.style.display = 'none';
            }
        });

        // البحث عن عناصر أخرى قد تكون مرتبطة بكاشف مانع الإعلانات
        const adBlockMessages = document.querySelectorAll('.adblock-message, .ad-blocker-notice, .adblock-warning, [class*="adblock"], [id*="adblock"]');
        adBlockMessages.forEach(element => {
            element.style.display = 'none';
        });
    }

    // إنشاء مراقب للتغييرات في DOM
    var observer = new MutationObserver(function(mutations) {
        if (!extensionEnabled) return;

        mutations.forEach(function(mutation) {
            // إذا تمت إضافة عناصر جديدة للصفحة
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                // تشغيل دالة إخفاء كاشف مانع الإعلانات
                hideAdBlockDetectors();
            }
        });
    });

    // بدء المراقبة
    if (document.body) {
        observer.observe(document.body, {
            'childList': true,
            'subtree': true
        });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            observer.observe(document.body, {
                'childList': true,
                'subtree': true
            });
        });
    }

    // تشغيل الدالة عند تحميل الصفحة
    window.addEventListener('load', function() {
        hideAdBlockDetectors();
    });

    // تشغيل الدالة مباشرة في حالة تحميل الصفحة بالفعل
    if (document.readyState === 'complete') {
        hideAdBlockDetectors();
    }
})();
