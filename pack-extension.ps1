# سكربت PowerShell لضغط الإضافة إلى ملف .zip

# تحديد المسار الحالي كمسار الإضافة
$extensionPath = Get-Location
$outputPath = Join-Path $extensionPath "CIH99-Extension.zip"

# إنشاء ملف .zip
Write-Host "جاري ضغط الإضافة من المسار: $extensionPath"
Write-Host "إلى الملف: $outputPath"

# حذف الملف إذا كان موجوداً
if (Test-Path $outputPath) {
    Remove-Item $outputPath -Force
    Write-Host "تم حذف الملف الموجود مسبقاً."
}

# قائمة الملفات التي سيتم استبعادها من الضغط
$excludeFiles = @(
    "*.zip",
    "*.crx",
    "*.pem",
    "*.ps1",
    ".git",
    ".gitignore",
    "password_generator.html"
)

# الحصول على جميع الملفات باستثناء المستبعدة
$files = Get-ChildItem -Path $extensionPath -Recurse -File | 
    Where-Object { 
        $include = $true
        foreach ($pattern in $excludeFiles) {
            if ($_.FullName -like "*$pattern*") {
                $include = $false
                break
            }
        }
        $include
    }

# إنشاء ملف .zip
Add-Type -AssemblyName System.IO.Compression.FileSystem
$zip = [System.IO.Compression.ZipFile]::Open($outputPath, 'Create')

# إضافة الملفات إلى الأرشيف
foreach ($file in $files) {
    $relativePath = $file.FullName.Substring($extensionPath.ToString().Length + 1)
    Write-Host "إضافة: $relativePath"
    
    $entry = [System.IO.Compression.ZipFileExtensions]::CreateEntryFromFile($zip, $file.FullName, $relativePath)
}

# إغلاق الأرشيف
$zip.Dispose()

Write-Host "تم إنشاء ملف الإضافة بنجاح: $outputPath"
Write-Host "يمكنك تغيير امتداد الملف من .zip إلى .crx إذا كنت ترغب في ذلك."

# إنشاء نسخة .crx
$crxPath = $outputPath -replace "\.zip$", ".crx"
Copy-Item -Path $outputPath -Destination $crxPath
Write-Host "تم إنشاء نسخة .crx: $crxPath"

# فتح المجلد الذي يحتوي على الملف
Invoke-Item (Split-Path $outputPath -Parent)
